<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Download,
  View,
  Star,
  Share,
  Document,
  ArrowLeft,
  ArrowRight,
  Loading
} from '@element-plus/icons-vue'
import PageHeader from '../components/PageHeader.vue'
import { getResource, getResources, incrementViews } from '../services/db/resourceService'
import { toggleFavorite, getFavorite } from '../services/db/favoriteService'

const route = useRoute()
const router = useRouter()
const resourceId = parseInt(route.params.id)
const resource = ref(null)
const loading = ref(true)
const relatedResources = ref([])

// PPT预览相关
const pptPreviewVisible = ref(false)
const pptSlides = ref([])
const currentSlideIndex = ref(0)

// 收藏相关
const isFavorited = ref(false)
const favoriteLoading = ref(false)

// 资源类型
const resourceTypes = [
  { label: '全部', value: 'all' },
  { label: 'PPT课件', value: 'ppt' },
  { label: '教学视频', value: 'video' },
  { label: '教案', value: 'document' },
  { label: '习题', value: 'exercise' }
]

// 加载资源详情
const loadResourceDetail = async () => {
  loading.value = true

  try {
    // 从数据库获取真实资源数据
    const resourceData = await getResource(resourceId)

    if (!resourceData) {
      ElMessage.error('资源不存在')
      router.push('/dashboard/resources')
      return
    }

    resource.value = resourceData

    // 增加浏览次数
    await incrementViews(resourceId)

    // 如果是PPT文件，处理PPT内容
    if (resourceData.type === 'ppt' && resourceData.fileUrl) {
      await processPPTContent(resourceData)
    }

    // 加载相关资源
    await loadRelatedResources()

    // 检查收藏状态
    await checkFavoriteStatus()

  } catch (error) {
    console.error('加载资源详情失败:', error)
    ElMessage.error('加载资源详情失败')
  } finally {
    loading.value = false
  }
}

// 处理PPT内容 - 使用pptx-preview库
const processPPTContent = async (resourceData) => {
  try {
    if (resourceData.fileUrl && resourceData.fileUrl.startsWith('data:')) {
      // 如果是Base64格式的PPT文件，使用pptx-preview库
      const response = await fetch(resourceData.fileUrl)
      const blob = await response.blob()

      // 创建File对象
      const file = new File([blob], resourceData.fileName, { type: resourceData.fileType })

      // 使用pptx-preview库处理PPT
      await initializePPTXPreview(file)

      console.log('PPTX预览初始化完成')
    }
  } catch (error) {
    console.error('PPT内容处理失败:', error)
    ElMessage.warning('PPT预览功能暂时不可用')
  }
}

// 加载相关资源
const loadRelatedResources = async () => {
  try {
    // 获取同类型的其他资源
    const allResources = await getResources({ type: resource.value.type })

    // 过滤掉当前资源，取前4个
    const filtered = allResources
      .filter(r => r.id !== resourceId)
      .slice(0, 4)

    relatedResources.value = filtered
  } catch (error) {
    console.error('加载相关资源失败:', error)
  }
}

// 下载资源
const downloadResource = () => {
  try {
    if (resource.value.fileUrl && resource.value.fileUrl.startsWith('data:')) {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = resource.value.fileUrl
      link.download = resource.value.fileName || `${resource.value.title}.${getFileExtension(resource.value.type)}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success(`开始下载: ${resource.value.title}`)

      // 增加下载次数
      resource.value.downloads = (resource.value.downloads || 0) + 1
    } else {
      ElMessage.warning('文件不可用')
    }
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 获取文件扩展名
const getFileExtension = (type) => {
  const extensions = {
    'ppt': 'pptx',
    'video': 'mp4',
    'document': 'pdf',
    'exercise': 'pdf'
  }
  return extensions[type] || 'file'
}

// 预览PPT
const previewPPT = () => {
  if (pptSlides.value.length > 0) {
    currentSlideIndex.value = 0
    pptPreviewVisible.value = true
  } else {
    ElMessage.warning('PPT预览不可用')
  }
}

// PPT导航
const prevSlide = () => {
  if (currentSlideIndex.value > 0) {
    currentSlideIndex.value--
  }
}

const nextSlide = () => {
  if (currentSlideIndex.value < pptSlides.value.length - 1) {
    currentSlideIndex.value++
  }
}

const goToSlide = (index) => {
  currentSlideIndex.value = index
}

// 检查收藏状态
const checkFavoriteStatus = async () => {
  try {
    const favorite = await getFavorite(resourceId)
    isFavorited.value = !!favorite
  } catch (error) {
    console.error('检查收藏状态失败:', error)
  }
}

// 切换收藏状态
const toggleFavoriteStatus = async () => {
  if (favoriteLoading.value) return

  favoriteLoading.value = true
  try {
    const result = await toggleFavorite(resourceId)
    isFavorited.value = result.isFavorited
    ElMessage.success(result.message)
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    favoriteLoading.value = false
  }
}

// 分享资源
const shareResource = () => {
  const url = window.location.href

  // 尝试使用现代浏览器的分享API
  if (navigator.share) {
    navigator.share({
      title: resource.value.title,
      text: resource.value.description,
      url: url
    }).catch(error => {
      console.log('分享取消或失败:', error)
    })
  } else {
    // 降级到复制链接
    navigator.clipboard.writeText(url).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      // 如果复制失败，显示链接
      ElMessageBox.alert(url, '分享链接', {
        confirmButtonText: '确定',
        type: 'info'
      })
    })
  }
}

// 使用pptx-preview库初始化PPT查看器
const initializePPTXPreview = async (file) => {
  try {
    console.log('使用pptx-preview初始化PPT查看器:', file.name)

    // 检查文件类型
    const isValidPPT = file.type === 'application/vnd.ms-powerpoint' ||
                      file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                      file.name.toLowerCase().endsWith('.ppt') ||
                      file.name.toLowerCase().endsWith('.pptx')

    if (!isValidPPT) {
      throw new Error('不支持的文件格式，请上传PPT或PPTX文件')
    }

    // 动态导入pptx-preview库
    const { renderPPTX } = await import('pptx-preview')

    // 使用pptx-preview库解析PPT
    const slides = await renderPPTX(file)

    // 将解析结果转换为我们需要的格式
    const slideImages = slides.map((slide, index) => {
      // 如果slide是canvas，转换为dataURL
      if (slide instanceof HTMLCanvasElement) {
        return slide.toDataURL('image/png')
      }
      // 如果slide是图片URL，直接使用
      if (typeof slide === 'string') {
        return slide
      }
      // 如果是其他格式，生成占位符
      return generatePlaceholderSlide(index + 1, file.name)
    })

    pptSlides.value = slideImages
    console.log(`PPTX预览生成完成，共${slideImages.length}张幻灯片`)

  } catch (error) {
    console.error('pptx-preview初始化失败:', error)
    // 如果pptx-preview失败，使用备用方案
    const fallbackSlides = await generateSimplePPTPreview(file)
    pptSlides.value = fallbackSlides
    console.log('使用备用方案生成PPT预览')
  }
}

// 生成占位符幻灯片
const generatePlaceholderSlide = (slideNumber, fileName) => {
  const canvas = document.createElement('canvas')
  canvas.width = 1024
  canvas.height = 768
  const ctx = canvas.getContext('2d')

  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, 1024, 768)

  // 绘制边框
  ctx.strokeStyle = '#e0e0e0'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, 1024, 768)

  // 绘制标题
  ctx.fillStyle = '#2c3e50'
  ctx.font = 'bold 36px "Microsoft YaHei", Arial, sans-serif'
  ctx.textAlign = 'center'
  ctx.fillText(`幻灯片 ${slideNumber}`, 512, 200)

  // 绘制文件名
  ctx.font = '18px "Microsoft YaHei", Arial, sans-serif'
  ctx.fillStyle = '#6c757d'
  ctx.fillText(`来源: ${fileName}`, 512, 250)

  // 绘制提示信息
  ctx.font = '24px "Microsoft YaHei", Arial, sans-serif'
  ctx.fillStyle = '#495057'
  ctx.fillText('PPT内容预览', 512, 350)

  ctx.font = '16px "Microsoft YaHei", Arial, sans-serif'
  ctx.fillStyle = '#6c757d'
  ctx.fillText('正在加载幻灯片内容...', 512, 400)

  return canvas.toDataURL('image/png')
}

// 生成简化的PPT预览
const generateSimplePPTPreview = async (file) => {
  try {
    // 对于PPTX文件，尝试简单解析
    if (file.name.toLowerCase().endsWith('.pptx')) {
      return await parseSimplePPTX(file)
    } else {
      // 对于其他格式，生成占位符
      return await generatePlaceholderImages(file)
    }
  } catch (error) {
    console.error('生成PPT预览失败:', error)
    // 如果解析失败，生成占位符
    return await generatePlaceholderImages(file)
  }
}

// 简化的PPTX解析函数
const parseSimplePPTX = async (file) => {
  const JSZip = (await import('jszip')).default

  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = async (e) => {
      try {
        const zip = new JSZip()
        const zipContent = await zip.loadAsync(e.target.result)

        // 查找幻灯片文件
        const slideFiles = Object.keys(zipContent.files)
          .filter(filename => filename.startsWith('ppt/slides/slide') && filename.endsWith('.xml'))
          .sort((a, b) => {
            const aNum = parseInt(a.match(/slide(\d+)\.xml/)[1])
            const bNum = parseInt(b.match(/slide(\d+)\.xml/)[1])
            return aNum - bNum
          })

        console.log(`找到 ${slideFiles.length} 张幻灯片`)

        const images = []

        // 为每张幻灯片生成简化的预览图
        for (let i = 0; i < slideFiles.length; i++) {
          const slideImage = await generateSimpleSlidePreview(i + 1, file.name, slideFiles.length)
          images.push(slideImage)
        }

        resolve(images)

      } catch (error) {
        console.error('简化PPTX解析失败:', error)
        // 如果解析失败，生成占位符图片
        const fallbackImages = await generatePlaceholderImages(file)
        resolve(fallbackImages)
      }
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    reader.readAsArrayBuffer(file)
  })
}

// 生成简化的幻灯片预览
const generateSimpleSlidePreview = async (slideNumber, fileName, totalSlides) => {
  const canvas = document.createElement('canvas')
  canvas.width = 1024
  canvas.height = 768
  const ctx = canvas.getContext('2d')

  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, 1024, 768)

  // 绘制边框
  ctx.strokeStyle = '#e0e0e0'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, 1024, 768)

  // 绘制标题区域
  ctx.fillStyle = '#f8f9fa'
  ctx.fillRect(50, 50, 924, 100)
  ctx.strokeStyle = '#dee2e6'
  ctx.lineWidth = 1
  ctx.strokeRect(50, 50, 924, 100)

  // 绘制标题文字
  ctx.fillStyle = '#2c3e50'
  ctx.font = 'bold 36px "Microsoft YaHei", Arial, sans-serif'
  ctx.textAlign = 'center'
  ctx.fillText(`幻灯片 ${slideNumber}`, 512, 110)

  // 绘制文件名
  ctx.font = '18px "Microsoft YaHei", Arial, sans-serif'
  ctx.fillStyle = '#6c757d'
  ctx.fillText(`来源: ${fileName}`, 512, 140)

  // 绘制内容区域
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(50, 180, 924, 500)
  ctx.strokeStyle = '#dee2e6'
  ctx.lineWidth = 1
  ctx.strokeRect(50, 180, 924, 500)

  // 绘制内容提示
  ctx.fillStyle = '#495057'
  ctx.font = '24px "Microsoft YaHei", Arial, sans-serif'
  ctx.textAlign = 'center'
  ctx.fillText('PPT内容预览', 512, 350)

  ctx.font = '18px "Microsoft YaHei", Arial, sans-serif'
  ctx.fillStyle = '#6c757d'
  ctx.fillText('此幻灯片包含以下可能的内容：', 512, 390)

  // 绘制内容列表
  const contentItems = [
    '• 文本内容和标题',
    '• 图片、图表和图形',
    '• 多媒体元素',
    '• 动画和过渡效果',
    '• 布局和设计元素'
  ]

  ctx.font = '16px "Microsoft YaHei", Arial, sans-serif'
  ctx.textAlign = 'left'
  contentItems.forEach((item, index) => {
    ctx.fillText(item, 200, 430 + index * 30)
  })

  // 绘制页脚
  ctx.fillStyle = '#e9ecef'
  ctx.fillRect(50, 700, 924, 50)
  ctx.strokeStyle = '#dee2e6'
  ctx.lineWidth = 1
  ctx.strokeRect(50, 700, 924, 50)

  // 绘制页脚信息
  ctx.fillStyle = '#6c757d'
  ctx.font = '14px "Microsoft YaHei", Arial, sans-serif'
  ctx.textAlign = 'left'
  ctx.fillText(`第 ${slideNumber} 页，共 ${totalSlides} 页`, 70, 730)

  ctx.textAlign = 'right'
  ctx.fillText('请下载原文件查看完整内容', 954, 730)

  // 转换为图片URL
  const imageUrl = canvas.toDataURL('image/png')
  return imageUrl
}

// 添加评论
const commentContent = ref('')
const submitComment = () => {
  if (!commentContent.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  // 初始化评论数组（如果不存在）
  if (!resource.value.comments) {
    resource.value.comments = []
  }

  // 添加新评论
  resource.value.comments.unshift({
    id: (resource.value.comments.length || 0) + 1,
    user: '当前用户',
    content: commentContent.value,
    time: new Date().toLocaleDateString(),
    avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0MDlFRkYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC4xMzQwMSAxNCA1IDE3LjEzNDEgNSAyMUg5SDE1SDE5QzE5IDE3LjEzNDEgMTUuODY2IDE0IDEyIDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cjwvc3ZnPgo='
  })

  // 清空输入
  commentContent.value = ''

  ElMessage.success('评论发布成功')
}

// 生成幻灯片图片
const generateSlideImage = async (slideContent, slideNumber, fileName, zipContent, actualSlideNumber = null) => {
  const canvas = document.createElement('canvas')
  canvas.width = 1024
  canvas.height = 768
  const ctx = canvas.getContext('2d')

  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, 1024, 768)

  // 绘制边框
  ctx.strokeStyle = '#e0e0e0'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, 1024, 768)

  try {
    // 解析幻灯片XML内容
    const slideData = await parseSlideXML(slideContent, zipContent, actualSlideNumber || slideNumber)

    // 绘制幻灯片内容
    await renderSlideContent(ctx, slideData, slideNumber)

  } catch (error) {
    console.error('解析幻灯片内容失败:', error)
    // 如果解析失败，绘制默认内容
    renderDefaultSlideContent(ctx, slideNumber, fileName)
  }

  // 转换为图片URL
  const imageUrl = canvas.toDataURL('image/png')
  return imageUrl
}

// 解析幻灯片XML内容
const parseSlideXML = async (slideXML, zipContent, slideNumber = 1) => {
  const slideData = {
    texts: [],
    images: [],
    shapes: [],
    title: '',
    content: []
  }

  try {
    console.log(`开始解析幻灯片 ${slideNumber}，XML长度: ${slideXML.length}`)

    // 检查XML是否包含实际内容
    if (!slideXML || slideXML.length < 100) {
      console.warn(`幻灯片 ${slideNumber} XML内容过短，可能为空`)
      return slideData
    }

    // 使用改进的文本提取方法
    const textContents = extractTextFromXML(slideXML)
    console.log(`幻灯片 ${slideNumber} 主要方法提取到的文本:`, textContents)

    // 如果主要方法没有提取到文本，尝试替代方法
    let allTexts = textContents
    if (textContents.length === 0) {
      console.log(`幻灯片 ${slideNumber} 主要方法未找到文本，尝试替代方法`)
      const alternativeTexts = extractTextAlternative(slideXML)
      allTexts = alternativeTexts
      console.log(`替代方法提取到的文本:`, alternativeTexts)
    }

    // 智能分析文本内容，区分标题和正文
    if (allTexts.length > 0) {
      // 过滤掉明显的元数据
      const validTexts = allTexts.filter(text =>
        text &&
        text.trim().length > 0 &&
        !isMetadataText(text)
      )

      console.log(`过滤后的有效文本:`, validTexts)

      if (validTexts.length > 0) {
        // 智能识别标题：通常是第一个较短的文本
        const firstText = validTexts[0]
        if (validTexts.length > 1 && firstText.length < 100) {
          slideData.title = firstText
          slideData.texts = validTexts.slice(1)
        } else {
          // 如果只有一个文本或第一个文本很长，将其作为内容
          slideData.texts = validTexts
        }
      }
    }

    // 查找图片引用
    const imageElements = slideXML.match(/r:embed="[^"]+"/g) || []
    console.log(`幻灯片 ${slideNumber} 找到图片引用:`, imageElements.length)

    for (const imgMatch of imageElements) {
      const embedId = imgMatch.match(/r:embed="([^"]+)"/)?.[1]
      if (embedId && zipContent) {
        try {
          const imagePath = await findImagePath(embedId, zipContent, slideNumber)
          if (imagePath) {
            slideData.images.push(imagePath)
          }
        } catch (error) {
          console.warn('查找图片失败:', error)
        }
      }
    }

    // 详细的调试信息
    const debugInfo = {
      slideNumber,
      title: slideData.title,
      textsCount: slideData.texts.length,
      contentCount: slideData.content.length,
      imagesCount: slideData.images.length,
      allTexts: slideData.texts,
      hasValidContent: slideData.title || slideData.texts.length > 0
    }

    console.log(`幻灯片 ${slideNumber} 最终解析结果:`, debugInfo)

    // 如果仍然没有找到任何有效内容，输出调试信息
    if (!debugInfo.hasValidContent) {
      console.warn(`幻灯片 ${slideNumber} 未找到有效内容`)
      console.log('XML结构分析:')
      console.log('- 包含 <a:t> 标签:', (slideXML.match(/<a:t[^>]*>/g) || []).length)
      console.log('- 包含 <p:txBody> 标签:', (slideXML.match(/<p:txBody[^>]*>/g) || []).length)
      console.log('- 包含 <a:p> 标签:', (slideXML.match(/<a:p[^>]*>/g) || []).length)
      console.log('- XML前2000字符:', slideXML.substring(0, 2000))

      // 尝试最后的备用方案：查找任何可能的文本
      const emergencyTexts = extractEmergencyText(slideXML)
      if (emergencyTexts.length > 0) {
        console.log('紧急文本提取找到:', emergencyTexts)
        slideData.texts = emergencyTexts
      } else {
        // 如果连紧急提取都失败，尝试最基础的文本搜索
        console.log('尝试最基础的文本搜索...')
        const basicTexts = extractBasicText(slideXML)
        if (basicTexts.length > 0) {
          console.log('基础文本搜索找到:', basicTexts)
          slideData.texts = basicTexts
        }
      }
    }

  } catch (error) {
    console.error(`解析幻灯片 ${slideNumber} XML失败:`, error)
    console.error('错误详情:', error.stack)
  }

  return slideData
}

// 紧急文本提取方法
const extractEmergencyText = (xmlString) => {
  const texts = []
  try {
    // 查找所有可能包含文本的标签
    const patterns = [
      />[^<]{4,}</g,  // 任何标签间的文本（至少4个字符）
    ]

    patterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(xmlString)) !== null) {
        const text = match[0].substring(1, match[0].length - 1).trim()
        if (text &&
            text.length > 3 &&
            !isMetadataText(text) &&
            !text.includes('<?') &&
            !text.includes('xmlns') &&
            !texts.includes(text)) {
          texts.push(text)
        }
      }
    })
  } catch (error) {
    console.error('紧急文本提取失败:', error)
  }
  return texts.slice(0, 10) // 最多返回10个文本
}

// 从XML中提取文本内容的辅助函数
const extractTextFromXML = (xmlString) => {
  const textContents = []
  const processedTexts = new Set()

  try {
    console.log('开始提取文本，XML长度:', xmlString.length)

    // 首先尝试使用DOM解析器进行精确解析
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml')

    // 检查解析是否成功
    const parseError = xmlDoc.querySelector('parsererror')
    if (!parseError) {
      console.log('DOM解析成功，开始查找文本节点')

      // 查找所有 a:t 元素（PowerPoint文本元素）
      const textElements = xmlDoc.querySelectorAll('a\\:t, t')
      console.log('找到文本元素数量:', textElements.length)

      textElements.forEach((element, index) => {
        const text = element.textContent.trim()
        console.log(`文本元素 ${index + 1}:`, text)

        if (text && text.length > 0 && !processedTexts.has(text)) {
          // 过滤掉明显的技术信息
          if (!isMetadataText(text)) {
            processedTexts.add(text)
            textContents.push(text)
          }
        }
      })

      // 如果没有找到 a:t 元素，尝试查找其他可能的文本容器
      if (textContents.length === 0) {
        console.log('未找到标准文本元素，尝试查找其他文本容器')
        const alternativeSelectors = [
          'p\\:txBody a\\:p a\\:r a\\:t',
          'a\\:p a\\:r a\\:t',
          'a\\:r a\\:t',
          'p\\:sp p\\:txBody a\\:p a\\:r a\\:t'
        ]

        alternativeSelectors.forEach(selector => {
          try {
            const elements = xmlDoc.querySelectorAll(selector)
            console.log(`选择器 ${selector} 找到元素:`, elements.length)
            elements.forEach(element => {
              const text = element.textContent.trim()
              if (text && text.length > 0 && !processedTexts.has(text) && !isMetadataText(text)) {
                processedTexts.add(text)
                textContents.push(text)
              }
            })
          } catch (e) {
            console.warn('选择器查询失败:', selector, e)
          }
        })
      }
    }

    // 如果DOM解析失败或没有找到文本，使用字符串匹配方法
    if (textContents.length === 0) {
      console.log('DOM解析未找到文本，使用字符串匹配方法')

      const patterns = [
        /<a:t[^>]*>([^<]+)<\/a:t>/g,
        /<t[^>]*>([^<]+)<\/t>/g,
        /<a:t>([^<]+)<\/a:t>/g,
        /<t>([^<]+)<\/t>/g
      ]

      patterns.forEach((pattern, patternIndex) => {
        let match
        while ((match = pattern.exec(xmlString)) !== null) {
          const text = match[1].trim()
          console.log(`模式 ${patternIndex + 1} 匹配到文本:`, text)

          if (text && text.length > 0 && !processedTexts.has(text) && !isMetadataText(text)) {
            const cleanText = cleanTextContent(text)
            if (cleanText.length > 0) {
              processedTexts.add(cleanText)
              textContents.push(cleanText)
            }
          }
        }
      })
    }

    console.log('最终提取到的文本数量:', textContents.length)
    console.log('提取到的文本内容:', textContents)

  } catch (error) {
    console.error('文本提取失败:', error)
  }

  return textContents
}

// 判断是否为元数据文本
const isMetadataText = (text) => {
  const metadataPatterns = [
    /^UTF-8$/i,
    /^http[s]?:\/\//,
    /xmlns/i,
    /namespace/i,
    /schema/i,
    /^[a-z]+:$/,
    /^[0-9]+$/,
    /^[a-f0-9-]{8,}$/i,
    /openxmlformats/i,
    /microsoft/i,
    /office/i,
    /presentation/i,
    /^rel$/i,
    /^id$/i,
    /^name$/i,
    /^val$/i
  ]

  return metadataPatterns.some(pattern => pattern.test(text)) || text.length < 2
}

// 清理文本内容
const cleanTextContent = (text) => {
  return text
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x[0-9A-Fa-f]+;/g, '') // 移除十六进制字符引用
    .replace(/&#\d+;/g, '') // 移除数字字符引用
    .replace(/\s+/g, ' ') // 合并多个空格
    .trim()
}

// 替代的文本提取方法
const extractTextAlternative = (xmlString) => {
  const textContents = []
  const processedTexts = new Set()

  try {
    console.log('使用替代方法提取文本，XML片段:', xmlString.substring(0, 500))

    // 方法1：深度递归查找所有文本节点
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml')

    // 检查解析错误
    const parseError = xmlDoc.querySelector('parsererror')
    if (!parseError) {
      console.log('替代方法DOM解析成功')

      // 递归查找所有文本节点，但过滤元数据
      function findValidTextNodes(node, depth = 0) {
        if (depth > 20) return // 防止无限递归

        if (node.nodeType === Node.TEXT_NODE) {
          const text = node.textContent.trim()
          if (text.length > 1 && !isMetadataText(text)) {
            const cleanText = cleanTextContent(text)
            if (cleanText.length > 0 && !processedTexts.has(cleanText)) {
              processedTexts.add(cleanText)
              textContents.push(cleanText)
              console.log(`深度 ${depth} 找到文本:`, cleanText)
            }
          }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          // 跳过明显的元数据元素
          const tagName = node.tagName.toLowerCase()
          if (!isMetadataElement(tagName)) {
            for (let child of node.childNodes) {
              findValidTextNodes(child, depth + 1)
            }
          }
        }
      }

      findValidTextNodes(xmlDoc)
    }

    // 方法2：使用更精确的正则表达式模式
    if (textContents.length === 0) {
      console.log('深度搜索未找到文本，尝试正则表达式模式')

      // 专门针对PowerPoint XML的模式
      const pptPatterns = [
        // PowerPoint文本运行
        /<a:r[^>]*>.*?<a:t[^>]*>([^<]+)<\/a:t>.*?<\/a:r>/gs,
        // 简单文本元素
        /<a:t[^>]*>([^<]+)<\/a:t>/g,
        // 段落中的文本
        /<a:p[^>]*>.*?<a:t[^>]*>([^<]+)<\/a:t>.*?<\/a:p>/gs,
        // 文本体中的内容
        /<p:txBody[^>]*>.*?<a:t[^>]*>([^<]+)<\/a:t>.*?<\/p:txBody>/gs
      ]

      pptPatterns.forEach((pattern, index) => {
        console.log(`尝试模式 ${index + 1}`)
        let match
        while ((match = pattern.exec(xmlString)) !== null) {
          const text = match[1].trim()
          if (text && text.length > 1 && !isMetadataText(text) && !processedTexts.has(text)) {
            const cleanText = cleanTextContent(text)
            if (cleanText.length > 0) {
              processedTexts.add(cleanText)
              textContents.push(cleanText)
              console.log(`模式 ${index + 1} 匹配到:`, cleanText)
            }
          }
        }
      })
    }

    // 方法3：如果仍然没有找到，尝试更宽泛的搜索
    if (textContents.length === 0) {
      console.log('精确模式未找到文本，尝试宽泛搜索')

      // 查找所有可能的文本内容，但严格过滤
      const broadPatterns = [
        />([^<]{3,})</g  // 至少3个字符的标签间文本
      ]

      broadPatterns.forEach(pattern => {
        let match
        while ((match = pattern.exec(xmlString)) !== null) {
          const text = match[1].trim()
          if (text &&
              text.length > 2 &&
              !text.includes('xmlns') &&
              !text.includes('http') &&
              !text.includes('schema') &&
              !isMetadataText(text) &&
              !processedTexts.has(text)) {
            const cleanText = cleanTextContent(text)
            if (cleanText.length > 0) {
              processedTexts.add(cleanText)
              textContents.push(cleanText)
              console.log('宽泛搜索找到:', cleanText)
            }
          }
        }
      })
    }

    console.log('替代方法最终结果:', textContents)

  } catch (error) {
    console.error('替代文本提取失败:', error)
  }

  return textContents
}

// 判断是否为元数据元素
const isMetadataElement = (tagName) => {
  const metadataElements = [
    'relationships', 'relationship', 'override', 'default',
    'coreproperties', 'properties', 'variant', 'vector',
    'headingpairs', 'titlesofparts', 'company', 'manager',
    'hyperlinks', 'hyperlink', 'extlst', 'ext'
  ]
  return metadataElements.includes(tagName)
}

// 查找图片路径
const findImagePath = async (embedId, zipContent, slideNumber) => {
  try {
    // 查找关系文件
    const relsPath = `ppt/slides/_rels/slide${slideNumber}.xml.rels`
    const relsFile = zipContent.files[relsPath]

    if (relsFile) {
      const relsContent = await relsFile.async('text')
      const parser = new DOMParser()
      const relsDoc = parser.parseFromString(relsContent, 'text/xml')

      const relationship = relsDoc.querySelector(`Relationship[Id="${embedId}"]`)
      if (relationship) {
        const target = relationship.getAttribute('Target')
        return `ppt/slides/${target}`
      }
    }
  } catch (error) {
    console.warn('解析关系文件失败:', error)
  }
  return null
}

// 渲染幻灯片内容
const renderSlideContent = async (ctx, slideData, slideNumber) => {
  const width = 1024
  const height = 768
  const leftMargin = 40
  const rightMargin = width - 40
  const topMargin = 40
  let currentY = topMargin

  // 绘制背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, width, height)

  // 绘制边框
  ctx.strokeStyle = '#e0e0e0'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, width, height)

  // 绘制标题
  if (slideData.title && slideData.title.trim()) {
    ctx.fillStyle = '#1a365d'
    ctx.font = 'bold 28px "Microsoft YaHei", Arial, sans-serif'
    ctx.textAlign = 'center'

    // 处理标题换行
    const titleLines = wrapText(ctx, slideData.title, rightMargin - leftMargin - 40)
    titleLines.forEach((line, index) => {
      ctx.fillText(line, width / 2, currentY + 40 + (index * 35))
    })

    currentY += 40 + (titleLines.length * 35) + 25

    // 绘制标题下划线
    ctx.strokeStyle = '#3182ce'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.moveTo(leftMargin + 50, currentY - 10)
    ctx.lineTo(rightMargin - 50, currentY - 10)
    ctx.stroke()

    currentY += 25
  } else {
    // 如果没有标题，显示幻灯片编号
    ctx.fillStyle = '#4a5568'
    ctx.font = 'bold 24px "Microsoft YaHei", Arial, sans-serif'
    ctx.textAlign = 'center'
    ctx.fillText(`幻灯片 ${slideNumber}`, width / 2, currentY + 30)
    currentY += 60
  }

  // 合并所有文本内容并过滤
  const allContent = [...slideData.texts, ...slideData.content]
    .filter(text => text && text.trim() && !isMetadataText(text))
    .map(text => text.trim())

  console.log(`幻灯片 ${slideNumber} 渲染内容:`, allContent)

  if (allContent.length > 0) {
    ctx.fillStyle = '#2d3748'
    ctx.font = '18px "Microsoft YaHei", Arial, sans-serif'
    ctx.textAlign = 'left'

    allContent.forEach((text, index) => {
      if (currentY < height - 100) {
        // 处理文本换行
        const lines = wrapText(ctx, text, rightMargin - leftMargin - 60)

        lines.forEach((line, lineIndex) => {
          if (currentY < height - 100) {
            // 如果是第一行且不是以•开头，添加项目符号
            if (lineIndex === 0 && !line.startsWith('•') && !line.startsWith('-')) {
              ctx.fillText(`• ${line}`, leftMargin + 20, currentY)
            } else {
              // 对于换行的内容，添加适当的缩进
              const indent = lineIndex === 0 ? 20 : 40
              ctx.fillText(line, leftMargin + indent, currentY)
            }
            currentY += 26
          }
        })

        // 在每个项目之间添加间距
        if (index < allContent.length - 1) {
          currentY += 10
        }
      }
    })

    // 如果内容太多，显示省略提示
    if (currentY >= height - 100) {
      ctx.fillStyle = '#718096'
      ctx.font = '16px "Microsoft YaHei", Arial, sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('...（内容较多，请下载原文件查看完整内容）', width / 2, height - 80)
    }

  } else {
    // 如果没有文本内容，显示提示信息
    ctx.fillStyle = '#718096'
    ctx.font = '18px "Microsoft YaHei", Arial, sans-serif'
    ctx.textAlign = 'center'

    const noContentMessages = [
      '此幻灯片可能包含以下内容：',
      '• 图片、图表或图形元素',
      '• 多媒体内容（音频/视频）',
      '• 复杂的布局和设计',
      '• 动画效果和交互元素',
      '',
      '请下载原文件以查看完整内容'
    ]

    noContentMessages.forEach((message, index) => {
      ctx.fillText(message, width / 2, height / 2 - 80 + (index * 24))
    })
  }

  // 绘制页脚信息
  ctx.fillStyle = '#a0aec0'
  ctx.font = '12px "Microsoft YaHei", Arial, sans-serif'
  ctx.textAlign = 'center'
  ctx.fillText(`第 ${slideNumber} 页`, width / 2, height - 15)

  // 绘制解析状态
  ctx.textAlign = 'left'
  const statusText = allContent.length > 0
    ? `成功解析 ${allContent.length} 个文本元素`
    : '未检测到文本内容'
  ctx.fillText(statusText, leftMargin, height - 15)

  // 绘制时间戳
  ctx.textAlign = 'right'
  ctx.fillText(new Date().toLocaleString(), rightMargin, height - 15)
}

// 文本换行辅助函数
const wrapText = (ctx, text, maxWidth) => {
  const words = text.split(' ')
  const lines = []
  let currentLine = ''

  for (let i = 0; i < words.length; i++) {
    const testLine = currentLine + words[i] + ' '
    const metrics = ctx.measureText(testLine)

    if (metrics.width > maxWidth && i > 0) {
      lines.push(currentLine.trim())
      currentLine = words[i] + ' '
    } else {
      currentLine = testLine
    }
  }

  if (currentLine.trim()) {
    lines.push(currentLine.trim())
  }

  return lines.length > 0 ? lines : [text]
}

// 渲染默认幻灯片内容
const renderDefaultSlideContent = (ctx, slideNumber, fileName) => {
  const width = 1024
  const height = 768

  // 绘制标题区域
  ctx.fillStyle = '#f5f5f5'
  ctx.fillRect(20, 20, width - 40, 80)

  // 绘制标题文字
  ctx.fillStyle = '#333333'
  ctx.font = 'bold 24px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`幻灯片 ${slideNumber}`, width / 2, 70)

  // 绘制文件名
  ctx.font = '16px Arial'
  ctx.fillStyle = '#666666'
  ctx.fillText(fileName, width / 2, 130)

  // 绘制内容区域
  ctx.fillStyle = '#fafafa'
  ctx.fillRect(20, 150, width - 40, height - 200)

  // 绘制内容文字
  ctx.fillStyle = '#333333'
  ctx.font = '18px Arial'
  ctx.textAlign = 'left'

  const lines = [
    '• 这是幻灯片的主要内容',
    '• 包含重要的教学信息',
    '• 支持多种媒体格式',
    '• 适合课堂教学使用',
    '• 请下载原文件查看完整内容'
  ]

  lines.forEach((line, index) => {
    ctx.fillText(line, 50, 200 + index * 40)
  })

  // 绘制页脚
  ctx.font = '14px Arial'
  ctx.fillStyle = '#999999'
  ctx.textAlign = 'center'
  ctx.fillText(`第 ${slideNumber} 页`, width / 2, height - 30)
}

// 生成占位符图片
const generatePlaceholderImages = async (file) => {
  const slideCount = Math.floor(Math.random() * 10) + 5 // 5-14张幻灯片
  const images = []

  for (let i = 1; i <= slideCount; i++) {
    const image = await generateSimpleSlidePreview(i, file.name, slideCount)
    images.push(image)
  }

  return images
}

// 查看其他资源
const viewResource = (id) => {
  router.push(`/dashboard/resource/${id}`)
}

onMounted(() => {
  loadResourceDetail()
})
</script>

<template>
  <div class="resource-detail-container">
    <PageHeader 
      title="资源详情" 
      subtitle="查看资源详细信息和评价"
      :show-back="true"
      back-text="返回资源库"
    />
    
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div class="card-panel">
          <el-skeleton-item variant="image" style="width: 100%; height: 300px" />
          <div style="padding: 20px">
            <el-skeleton-item variant="h1" style="width: 50%" />
            <div style="display: flex; align-items: center; margin: 16px 0">
              <el-skeleton-item variant="text" style="margin-right: 16px" />
              <el-skeleton-item variant="text" style="width: 30%" />
            </div>
            <el-skeleton-item variant="text" style="width: 100%" />
            <el-skeleton-item variant="text" style="width: 100%" />
          </div>
        </div>
      </template>
      
      <template #default>
        <div v-if="resource" class="resource-content">
          <!-- 资源基本信息 -->
          <div class="resource-header card-panel">
            <div class="resource-banner">
              <img :src="resource.coverUrl" :alt="resource.title" class="resource-image">
            </div>
            
            <div class="resource-main-info">
              <div class="resource-title-area">
                <h1 class="resource-title">{{ resource.title }}</h1>
                <el-tag :type="resourceTypes.find(t => t.value === resource.type)?.value === 'document' ? 'success' : 
                      resourceTypes.find(t => t.value === resource.type)?.value === 'ppt' ? 'primary' : 
                      resourceTypes.find(t => t.value === resource.type)?.value === 'video' ? 'danger' : 'warning'">
                  {{ resourceTypes.find(t => t.value === resource.type)?.label || '通用' }}
                </el-tag>
              </div>
              
              <div class="resource-description">
                {{ resource.description }}
              </div>
              
              <div class="resource-meta">
                <div class="meta-item">
                  <span class="meta-label">上传时间：</span>
                  <span class="meta-value">{{ resource.uploadTime }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">更新时间：</span>
                  <span class="meta-value">{{ resource.updateTime }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">文件大小：</span>
                  <span class="meta-value">{{ resource.fileSize }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">文件格式：</span>
                  <span class="meta-value">{{ resource.format }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">下载次数：</span>
                  <span class="meta-value">{{ resource.downloads }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">浏览次数：</span>
                  <span class="meta-value">{{ resource.views }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">资源作者：</span>
                  <span class="meta-value">{{ resource.author }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">资源评分：</span>
                  <span class="meta-value">
                    <el-rate v-model="resource.rating" disabled text-color="#ff9900" />
                    <span class="rating-text">{{ resource.rating }}</span>
                  </span>
                </div>
              </div>
              
              <div class="resource-actions">
                <el-button type="primary" size="large" @click="downloadResource">
                  <el-icon><Download /></el-icon>
                  立即下载
                </el-button>
                <el-button v-if="resource.type === 'ppt'" type="success" size="large" @click="previewPPT">
                  <el-icon><View /></el-icon>
                  预览PPT
                </el-button>
                <el-button
                  size="large"
                  :type="isFavorited ? 'warning' : 'default'"
                  :loading="favoriteLoading"
                  @click="toggleFavoriteStatus"
                >
                  <el-icon><Star /></el-icon>
                  {{ isFavorited ? '已收藏' : '收藏资源' }}
                </el-button>
                <el-button size="large" @click="shareResource">
                  <el-icon><Share /></el-icon>
                  分享资源
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 资源评论 -->
          <div class="resource-comments card-panel">
            <div class="panel-header">
              <h2 class="panel-title">资源评论 ({{ resource.comments?.length || 0 }})</h2>
            </div>

            <div class="comment-form">
              <el-input
                v-model="commentContent"
                type="textarea"
                rows="3"
                placeholder="分享您对这个资源的看法和经验..."
              />
              <div class="form-actions">
                <el-button type="primary" @click="submitComment">发表评论</el-button>
              </div>
            </div>

            <div class="comment-list">
              <div v-if="!resource.comments || resource.comments.length === 0" class="no-comments">
                暂无评论，来发表第一条评论吧！
              </div>

              <div v-else class="comment-item" v-for="comment in resource.comments" :key="comment.id">
                <el-avatar :src="comment.avatar" :size="40" />
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-user">{{ comment.user }}</span>
                    <span class="comment-time">{{ comment.time }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 相关资源 -->
          <div class="related-resources card-panel">
            <div class="panel-header">
              <h2 class="panel-title">相关资源推荐</h2>
            </div>
            
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="12" :lg="6" v-for="related in relatedResources" :key="related.id">
                <el-card class="related-card" shadow="hover" @click="viewResource(related.id)">
                  <div class="related-cover">
                    <img :src="related.coverUrl" :alt="related.title">
                    <div class="related-type">
                      {{ resourceTypes.find(t => t.value === related.type)?.label || '通用' }}
                    </div>
                  </div>
                  <div class="related-info">
                    <h3 class="related-title" :title="related.title">{{ related.title }}</h3>
                    <div class="related-meta">
                      <span><el-icon><Download /></el-icon> {{ related.downloads }}</span>
                      <span><el-icon><Document /></el-icon> {{ related.fileSize }}</span>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
    </el-skeleton>

    <!-- PPT预览对话框 -->
    <el-dialog
      v-model="pptPreviewVisible"
      :title="`PPT预览 - ${resource?.title || ''}`"
      width="90%"
      top="5vh"
      :before-close="() => { pptPreviewVisible = false; currentSlideIndex = 0 }"
    >
      <div v-if="pptSlides.length > 0" class="ppt-preview-container">
        <!-- PPT信息 -->
        <div class="ppt-info">
          <div class="ppt-meta">
            <span><strong>文件名：</strong>{{ resource.fileName }}</span>
            <span><strong>幻灯片数量：</strong>{{ pptSlides.length }}</span>
            <span><strong>当前：</strong>{{ currentSlideIndex + 1 }} / {{ pptSlides.length }}</span>
          </div>
        </div>

        <!-- 幻灯片显示区域 -->
        <div class="slide-viewer">
          <div class="slide-container">
            <img
              :src="pptSlides[currentSlideIndex]"
              :alt="`幻灯片 ${currentSlideIndex + 1}`"
              class="slide-image"
            />
          </div>

          <!-- 导航按钮 -->
          <div class="slide-navigation">
            <el-button
              :disabled="currentSlideIndex === 0"
              @click="prevSlide"
              circle
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>

            <span class="slide-counter">
              {{ currentSlideIndex + 1 }} / {{ pptSlides.length }}
            </span>

            <el-button
              :disabled="currentSlideIndex === pptSlides.length - 1"
              @click="nextSlide"
              circle
            >
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 幻灯片缩略图 -->
        <div class="slide-thumbnails">
          <div class="thumbnails-container">
            <div
              v-for="(slide, index) in pptSlides"
              :key="index"
              :class="['thumbnail', { active: index === currentSlideIndex }]"
              @click="goToSlide(index)"
            >
              <img :src="slide" :alt="`幻灯片 ${index + 1}`" />
              <span class="thumbnail-number">{{ index + 1 }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="ppt-loading">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>正在加载PPT预览...</p>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pptPreviewVisible = false">关闭</el-button>
          <el-button type="primary" @click="downloadResource">下载PPT</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.resource-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.resource-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.resource-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.resource-banner {
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 8px;
  position: relative;
}

.resource-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.resource-main-info {
  padding: 0 16px;
}

.resource-title-area {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.resource-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.resource-description {
  font-size: 14px;
  color: var(--text-regular);
  line-height: 1.6;
  margin-bottom: 24px;
}

.resource-meta {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  color: var(--text-secondary);
  font-size: 14px;
  margin-right: 8px;
}

.meta-value {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.rating-text {
  margin-left: 8px;
  color: #ff9900;
  font-weight: 600;
}

.resource-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.resource-comments {
  margin-top: 24px;
}

.comment-form {
  margin-bottom: 24px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  display: flex;
  gap: 16px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-light);
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-user {
  font-weight: 500;
  color: var(--text-primary);
}

.comment-time {
  font-size: 13px;
  color: var(--text-secondary);
}

.comment-text {
  color: var(--text-regular);
  line-height: 1.5;
}

.no-comments {
  text-align: center;
  padding: 24px;
  color: var(--text-secondary);
}

.related-resources {
  margin-top: 24px;
}

.related-card {
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 16px;
}

.related-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.related-cover {
  position: relative;
  height: 140px;
  overflow: hidden;
}

.related-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.related-card:hover .related-cover img {
  transform: scale(1.08);
}

.related-type {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 8px;
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.related-info {
  padding: 12px;
}

.related-title {
  font-size: 14px;
  margin: 0 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.related-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
}

.related-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* PPT预览样式 */
.ppt-preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 70vh;
}

.ppt-info {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.ppt-meta {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  font-size: 14px;
  color: #666;
}

.slide-viewer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.slide-container {
  width: 100%;
  max-width: 800px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.slide-image {
  width: 100%;
  height: auto;
  display: block;
}

.slide-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.slide-counter {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  min-width: 80px;
  text-align: center;
}

.slide-thumbnails {
  max-height: 120px;
  overflow-y: auto;
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.thumbnails-container {
  display: flex;
  gap: 8px;
  padding: 0 8px;
}

.thumbnail {
  position: relative;
  width: 80px;
  height: 60px;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  flex-shrink: 0;
}

.thumbnail:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.thumbnail.active {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-number {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.ppt-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.loading-icon {
  font-size: 32px;
  margin-bottom: 16px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .resource-meta {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .resource-actions {
    flex-direction: column;
  }

  .resource-banner {
    height: 200px;
  }

  .ppt-meta {
    flex-direction: column;
    gap: 8px;
  }

  .slide-navigation {
    gap: 8px;
  }

  .thumbnails-container {
    justify-content: center;
  }

  .thumbnail {
    width: 60px;
    height: 45px;
  }
}
</style> 