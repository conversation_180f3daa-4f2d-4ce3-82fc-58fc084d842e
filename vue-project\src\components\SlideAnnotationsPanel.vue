<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import {
  getSlideAnnotations,
  addSlideAnnotation,
  deleteSlideAnnotation
} from '../services/db/classroomService';

const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  currentSlide: {
    type: Number,
    required: true
  },
  role: {
    type: String,
    default: 'student' // 'teacher' 或 'student'
  }
});

// 新注释表单
const newAnnotation = ref('');
const isSubmitting = ref(false);

// 当前幻灯片的所有注释
const annotations = ref([]);

// 是否显示其他用户的注释
const showOthersAnnotations = ref(true);

// 计算当前用户的注释
const userAnnotations = computed(() => {
  return annotations.value.filter(annotation => annotation.userId === props.userId);
});

// 计算其他用户的注释
const othersAnnotations = computed(() => {
  return annotations.value.filter(annotation => annotation.userId !== props.userId);
});

// 加载当前幻灯片的注释
const loadAnnotations = async () => {
  try {
    const slideAnnotations = await getSlideAnnotations(props.currentSlide);
    annotations.value = slideAnnotations;
  } catch (error) {
    console.error('获取注释失败:', error);
    ElMessage.error('获取注释失败');
  }
};

// 添加注释
const submitAnnotation = async () => {
  if (!newAnnotation.value.trim()) {
    ElMessage.warning('请输入注释内容');
    return;
  }
  
  isSubmitting.value = true;
  
  try {
    const annotation = {
      text: newAnnotation.value,
      position: { x: 0, y: 0 }, // 默认位置
      color: props.role === 'teacher' ? '#409EFF' : '#67C23A'
    };
    
    await addSlideAnnotation(props.userId, props.currentSlide, annotation);
    newAnnotation.value = '';
    await loadAnnotations();
    
    ElMessage.success('注释已添加');
  } catch (error) {
    console.error('添加注释失败:', error);
    ElMessage.error('添加注释失败');
  } finally {
    isSubmitting.value = false;
  }
};

// 删除注释
const removeAnnotation = async (annotationId) => {
  try {
    await deleteSlideAnnotation(props.userId, annotationId);
    await loadAnnotations();
    ElMessage.success('注释已删除');
  } catch (error) {
    console.error('删除注释失败:', error);
    ElMessage.error('删除注释失败');
  }
};

// 确认删除注释
const confirmRemoveAnnotation = (annotationId) => {
  ElMessageBox.confirm(
    '确定要删除这条注释吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    removeAnnotation(annotationId);
  }).catch(() => {});
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit'
  });
};

// 获取用户信息
const getUserInfo = (userId) => {
  // 模拟用户数据，实际应该从用户系统获取
  const users = {
    'teacher1': { name: '张老师', role: 'teacher', avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png' },
    'student1': { name: '张三', role: 'student', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=1' },
    'student2': { name: '李四', role: 'student', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=2' },
    'student3': { name: '王五', role: 'student', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=3' }
  };
  
  return users[userId] || { name: '未知用户', role: 'unknown', avatar: '' };
};

// 监听当前幻灯片变化
watch(() => props.currentSlide, (newVal) => {
  if (newVal) {
    loadAnnotations();
  }
});

// 定时刷新
let refreshTimer = null;

onMounted(() => {
  loadAnnotations();
  
  // 每10秒刷新一次
  refreshTimer = setInterval(loadAnnotations, 10000);
});

onBeforeUnmount(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
});
</script>

<template>
  <div class="slide-annotations-panel">
    <div class="annotations-header">
      <h3 class="annotations-title">幻灯片注释</h3>
      
      <div class="annotations-filter">
        <el-switch
          v-model="showOthersAnnotations"
          active-text="显示他人注释"
          inactive-text="仅显示我的"
        />
      </div>
    </div>
    
    <div class="annotation-form">
      <el-input
        v-model="newAnnotation"
        type="textarea"
        :rows="2"
        placeholder="添加新注释..."
        :disabled="isSubmitting"
      />
      <el-button 
        type="primary" 
        @click="submitAnnotation"
        :loading="isSubmitting"
        :disabled="!newAnnotation.trim()"
      >
        添加注释
      </el-button>
    </div>
    
    <div class="annotations-list">
      <div class="annotations-section">
        <div class="section-title">我的注释</div>
        
        <div v-if="userAnnotations.length === 0" class="empty-annotations">
          <el-empty description="暂无注释" :image-size="100">
            <template #description>
              <p>您还没有为当前幻灯片添加注释</p>
            </template>
          </el-empty>
        </div>
        
        <div 
          v-for="annotation in userAnnotations"
          :key="annotation.id"
          class="annotation-item"
          :style="{ borderLeftColor: annotation.color }"
        >
          <div class="annotation-content">{{ annotation.text }}</div>
          
          <div class="annotation-info">
            <div class="annotation-time">{{ formatTime(annotation.timestamp) }}</div>
            
            <el-button 
              type="danger" 
              size="small" 
              circle
              @click="confirmRemoveAnnotation(annotation.id)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="showOthersAnnotations" class="annotations-section">
        <div class="section-title">其他注释</div>
        
        <div v-if="othersAnnotations.length === 0" class="empty-annotations">
          <el-empty description="暂无注释" :image-size="100">
            <template #description>
              <p>其他人还没有为当前幻灯片添加注释</p>
            </template>
          </el-empty>
        </div>
        
        <div 
          v-for="annotation in othersAnnotations"
          :key="annotation.id"
          class="annotation-item"
          :style="{ borderLeftColor: annotation.color }"
        >
          <div class="annotation-header">
            <el-avatar 
              :size="24" 
              :src="getUserInfo(annotation.userId).avatar"
            />
            <span class="annotation-author">{{ getUserInfo(annotation.userId).name }}</span>
            <el-tag 
              size="small" 
              :type="getUserInfo(annotation.userId).role === 'teacher' ? 'primary' : 'success'"
              class="role-tag"
            >
              {{ getUserInfo(annotation.userId).role === 'teacher' ? '教师' : '学生' }}
            </el-tag>
          </div>
          
          <div class="annotation-content">{{ annotation.text }}</div>
          
          <div class="annotation-info">
            <div class="annotation-time">{{ formatTime(annotation.timestamp) }}</div>
            
            <div v-if="props.role === 'teacher'" class="annotation-actions">
              <el-button 
                type="danger" 
                size="small" 
                circle
                @click="confirmRemoveAnnotation(annotation.id)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.slide-annotations-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.annotations-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.annotations-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.annotation-form {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.annotation-form .el-button {
  margin-top: 10px;
  align-self: flex-end;
}

.annotations-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.annotations-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 10px;
}

.empty-annotations {
  margin: 20px 0;
}

.annotation-item {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  border-left: 4px solid #409eff;
}

.annotation-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.annotation-author {
  margin-left: 8px;
  font-weight: 500;
}

.role-tag {
  margin-left: 5px;
}

.annotation-content {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  word-break: break-all;
  white-space: pre-line;
}

.annotation-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

.annotation-time {
  color: #909399;
}
</style> 