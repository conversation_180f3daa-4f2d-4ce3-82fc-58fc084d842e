import { getDB, STORES } from './index';

// 创建临时存储，模拟实时数据（实际项目中应使用WebSocket等）
const tempStorage = {
  // 当前控制权
  currentController: 'teacher1',
  // 当前PPT页码
  currentSlide: 1,
  // 总页数
  totalSlides: 10,
  // 白板标记
  whiteboardMarks: [],
  // 活跃投票ID
  activeVoteId: null,
  // PPT文件信息
  pptInfo: {
    id: 'default-ppt',
    title: '示例课件',
    slideCount: 10,
    currentIndex: 1,
    images: Array.from({length: 10}, (_, i) => `https://picsum.photos/800/600?random=${i+1}`)
  },
  // 学生在线列表
  onlineStudents: [
    { id: 'student1', name: '张三', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=1', status: 'active' },
    { id: 'student2', name: '李四', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=2', status: 'active' },
    { id: 'student3', name: '王五', avatar: 'https://xsgames.co/randomusers/avatar.php?g=pixel&key=3', status: 'inactive' }
  ],
  // 注意力跟踪
  attentionTracking: {
    focused: ['student1', 'student2'],
    distracted: ['student3'],
    lastUpdate: new Date().toISOString()
  },
  // PPT注释
  slideAnnotations: {
    // 为第1张幻灯片添加一些示例笔记
    1: [
      {
        id: 'demo_note_1',
        userId: 'teacher1',
        timestamp: Date.now() - 300000, // 5分钟前
        text: '这是第一张幻灯片的重点内容，需要重点讲解',
        position: { x: 0, y: 0 },
        color: '#409EFF'
      }
    ]
  },
  // 课堂问题
  classQuestions: [],
  // 抢答列表
  quickResponses: []
};

/**
 * 获取当前控制者
 * @returns {Promise<string>} 控制者ID
 */
export async function getCurrentController() {
  return tempStorage.currentController;
}

/**
 * 转移控制权
 * @param {string} teacherId - 教师ID
 * @param {string} studentId - 学生ID
 * @returns {Promise<boolean>} 是否转移成功
 */
export async function transferControl(teacherId, studentId) {
  // 检查当前控制者是否为教师
  if (tempStorage.currentController !== teacherId) {
    throw new Error('只有当前控制者才能转移控制权');
  }
  
  tempStorage.currentController = studentId;
  return true;
}

/**
 * 收回控制权
 * @param {string} teacherId - 教师ID
 * @returns {Promise<boolean>} 是否收回成功
 */
export async function recallControl(teacherId) {
  tempStorage.currentController = teacherId;
  return true;
}

/**
 * 切换PPT页面
 * @param {string} userId - 控制者ID
 * @param {number} slideNumber - 页码
 * @returns {Promise<boolean>} 是否切换成功
 */
export async function changeSlide(userId, slideNumber) {
  // 检查是否有控制权
  if (tempStorage.currentController !== userId) {
    throw new Error('没有控制权限');
  }
  
  // 页码范围检查
  if (slideNumber < 1 || slideNumber > tempStorage.totalSlides) {
    throw new Error('页码超出范围');
  }
  
  tempStorage.currentSlide = slideNumber;
  tempStorage.pptInfo.currentIndex = slideNumber;
  return true;
}

/**
 * 获取当前PPT页码
 * @returns {Promise<number>} 页码
 */
export async function getCurrentSlide() {
  return tempStorage.currentSlide;
}

/**
 * 获取PPT总页数
 * @returns {Promise<number>} 总页数
 */
export async function getTotalSlides() {
  return tempStorage.totalSlides;
}

/**
 * 获取当前PPT信息
 * @returns {Promise<Object>} PPT信息
 */
export async function getPPTInfo() {
  return tempStorage.pptInfo;
}

/**
 * 更新PPT信息
 * @param {Object} pptInfo - 新的PPT信息
 * @returns {Promise<boolean>} 是否更新成功
 */
export async function updatePPTInfo(pptInfo) {
  tempStorage.pptInfo = { ...tempStorage.pptInfo, ...pptInfo };
  tempStorage.totalSlides = pptInfo.slideCount || tempStorage.totalSlides;
  return true;
}

/**
 * 添加白板标记
 * @param {string} userId - 控制者ID
 * @param {Object} mark - 标记信息 {type, color, thickness, points}
 * @returns {Promise<boolean>} 是否添加成功
 */
export async function addWhiteboardMark(userId, mark) {
  // 检查是否有控制权
  if (tempStorage.currentController !== userId) {
    throw new Error('没有控制权限');
  }
  
  // 添加页码和时间戳
  mark.slideNumber = tempStorage.currentSlide;
  mark.timestamp = Date.now();
  mark.userId = userId;
  mark.id = Date.now() + Math.random().toString(36).substr(2, 9);
  
  tempStorage.whiteboardMarks.push(mark);
  return mark.id;
}

/**
 * 清除当前页面的白板标记
 * @param {string} userId - 控制者ID
 * @returns {Promise<boolean>} 是否清除成功
 */
export async function clearWhiteboardMarks(userId) {
  // 检查是否有控制权
  if (tempStorage.currentController !== userId) {
    throw new Error('没有控制权限');
  }
  
  // 只移除当前页面的标记
  tempStorage.whiteboardMarks = tempStorage.whiteboardMarks.filter(
    mark => mark.slideNumber !== tempStorage.currentSlide
  );
  
  return true;
}

/**
 * 获取当前页面的白板标记
 * @returns {Promise<Array>} 标记列表
 */
export async function getCurrentSlideMarks() {
  return tempStorage.whiteboardMarks.filter(
    mark => mark.slideNumber === tempStorage.currentSlide
  );
}

/**
 * 删除特定白板标记
 * @param {string} userId - 用户ID
 * @param {string} markId - 标记ID
 * @returns {Promise<boolean>} 是否删除成功
 */
export async function deleteWhiteboardMark(userId, markId) {
  // 检查是否有控制权
  if (tempStorage.currentController !== userId) {
    throw new Error('没有控制权限');
  }
  
  const markIndex = tempStorage.whiteboardMarks.findIndex(mark => mark.id === markId);
  if (markIndex !== -1) {
    tempStorage.whiteboardMarks.splice(markIndex, 1);
    return true;
  }
  
  return false;
}

/**
 * 更新白板标记样式
 * @param {string} userId - 用户ID
 * @param {string} markId - 标记ID
 * @param {Object} style - 新样式 {color, thickness}
 * @returns {Promise<boolean>} 是否更新成功
 */
export async function updateWhiteboardMarkStyle(userId, markId, style) {
  // 检查是否有控制权
  if (tempStorage.currentController !== userId) {
    throw new Error('没有控制权限');
  }
  
  const mark = tempStorage.whiteboardMarks.find(m => m.id === markId);
  if (mark) {
    Object.assign(mark, style);
    return true;
  }
  
  return false;
}

/**
 * 发起投票
 * @param {string} teacherId - 教师ID
 * @param {Object} vote - 投票信息
 * @returns {Promise<number>} 投票ID
 */
export async function startVote(teacherId, vote) {
  // 验证教师身份
  // 实际应用中应该验证用户角色
  
  // 创建投票
  const db = await getDB();
  
  // 设置投票信息
  vote.createdBy = teacherId;
  vote.createdAt = new Date().toISOString();
  vote.status = 'active';
  vote.totalVotes = 0;
  
  // 初始化选项计数
  vote.options = vote.options.map(option => {
    return typeof option === 'string' ? { text: option, count: 0 } : { ...option, count: 0 };
  });
  
  const voteId = await db.add(STORES.VOTES, vote);
  
  // 设置当前活跃投票
  tempStorage.activeVoteId = voteId;
  
  return voteId;
}

/**
 * 获取当前活跃投票
 * @returns {Promise<Object>} 投票信息
 */
export async function getActiveVote() {
  if (!tempStorage.activeVoteId) {
    return null;
  }
  
  const db = await getDB();
  return await db.get(STORES.VOTES, tempStorage.activeVoteId);
}

/**
 * 结束当前投票
 * @param {string} teacherId - 教师ID
 * @returns {Promise<boolean>} 是否结束成功
 */
export async function endActiveVote(teacherId) {
  if (!tempStorage.activeVoteId) {
    return false;
  }
  
  const db = await getDB();
  const vote = await db.get(STORES.VOTES, tempStorage.activeVoteId);
  
  if (vote && vote.createdBy === teacherId) {
    vote.status = 'ended';
    vote.endTime = new Date().toISOString();
    await db.put(STORES.VOTES, vote);
    
    // 清除当前活跃投票
    tempStorage.activeVoteId = null;
    
    return true;
  }
  
  return false;
}

/**
 * 获取在线学生列表
 * @returns {Promise<Array>} 学生列表
 */
export async function getOnlineStudents() {
  return tempStorage.onlineStudents;
}

/**
 * 更新学生状态
 * @param {string} studentId - 学生ID
 * @param {string} status - 状态 ('active', 'inactive', 'away')
 * @returns {Promise<boolean>} 是否更新成功
 */
export async function updateStudentStatus(studentId, status) {
  const studentIndex = tempStorage.onlineStudents.findIndex(s => s.id === studentId);
  if (studentIndex !== -1) {
    tempStorage.onlineStudents[studentIndex].status = status;
    return true;
  }
  return false;
}

/**
 * 记录学生注意力状态
 * @param {string} studentId - 学生ID
 * @param {boolean} isFocused - 是否专注
 * @returns {Promise<boolean>} 是否记录成功
 */
export async function trackStudentAttention(studentId, isFocused) {
  const focusedIndex = tempStorage.attentionTracking.focused.indexOf(studentId);
  const distractedIndex = tempStorage.attentionTracking.distracted.indexOf(studentId);
  
  if (isFocused) {
    if (distractedIndex !== -1) {
      tempStorage.attentionTracking.distracted.splice(distractedIndex, 1);
    }
    if (focusedIndex === -1) {
      tempStorage.attentionTracking.focused.push(studentId);
    }
  } else {
    if (focusedIndex !== -1) {
      tempStorage.attentionTracking.focused.splice(focusedIndex, 1);
    }
    if (distractedIndex === -1) {
      tempStorage.attentionTracking.distracted.push(studentId);
    }
  }
  
  tempStorage.attentionTracking.lastUpdate = new Date().toISOString();
  return true;
}

/**
 * 获取注意力跟踪数据
 * @returns {Promise<Object>} 注意力数据
 */
export async function getAttentionTrackingData() {
  return tempStorage.attentionTracking;
}

/**
 * 添加PPT注释
 * @param {string} userId - 用户ID
 * @param {number} slideNumber - 幻灯片页码
 * @param {Object} annotation - 注释信息 {text, position, color}
 * @returns {Promise<string>} 注释ID
 */
export async function addSlideAnnotation(userId, slideNumber, annotation) {
  console.log('数据库服务：添加幻灯片注释', { userId, slideNumber, annotation });

  if (!tempStorage.slideAnnotations[slideNumber]) {
    tempStorage.slideAnnotations[slideNumber] = [];
  }

  const annotationObj = {
    id: Date.now() + Math.random().toString(36).substr(2, 9),
    userId,
    timestamp: Date.now(),
    ...annotation
  };

  tempStorage.slideAnnotations[slideNumber].push(annotationObj);

  console.log('数据库服务：注释添加成功', annotationObj);
  console.log('数据库服务：当前存储状态', tempStorage.slideAnnotations);

  return annotationObj.id;
}

/**
 * 获取幻灯片注释
 * @param {number} slideNumber - 幻灯片页码
 * @returns {Promise<Array>} 注释列表
 */
export async function getSlideAnnotations(slideNumber) {
  console.log('数据库服务：获取幻灯片注释', { slideNumber });
  const annotations = tempStorage.slideAnnotations[slideNumber] || [];
  console.log('数据库服务：返回注释数据', annotations);
  return annotations;
}

/**
 * 删除幻灯片注释
 * @param {string} userId - 用户ID
 * @param {string} annotationId - 注释ID
 * @returns {Promise<boolean>} 是否删除成功
 */
export async function deleteSlideAnnotation(userId, annotationId) {
  for (const slideNumber in tempStorage.slideAnnotations) {
    const annotations = tempStorage.slideAnnotations[slideNumber];
    const index = annotations.findIndex(a => a.id === annotationId && a.userId === userId);
    
    if (index !== -1) {
      annotations.splice(index, 1);
      return true;
    }
  }
  
  return false;
}

/**
 * 提交课堂问题
 * @param {string} studentId - 学生ID
 * @param {Object} question - 问题内容 {content, type}
 * @returns {Promise<string>} 问题ID
 */
export async function submitClassQuestion(studentId, question) {
  const questionObj = {
    id: Date.now() + Math.random().toString(36).substr(2, 9),
    studentId,
    timestamp: Date.now(),
    answered: false,
    answer: '',
    likes: 0,
    ...question
  };
  
  tempStorage.classQuestions.push(questionObj);
  return questionObj.id;
}

/**
 * 获取课堂问题列表
 * @param {Object} filters - 筛选条件 {answered, studentId}
 * @returns {Promise<Array>} 问题列表
 */
export async function getClassQuestions(filters = {}) {
  let questions = [...tempStorage.classQuestions];
  
  if (filters.answered !== undefined) {
    questions = questions.filter(q => q.answered === filters.answered);
  }
  
  if (filters.studentId) {
    questions = questions.filter(q => q.studentId === filters.studentId);
  }
  
  return questions.sort((a, b) => b.timestamp - a.timestamp);
}

/**
 * 回答课堂问题
 * @param {string} teacherId - 教师ID
 * @param {string} questionId - 问题ID
 * @param {string} answer - 回答内容
 * @returns {Promise<boolean>} 是否回答成功
 */
export async function answerClassQuestion(teacherId, questionId, answer) {
  const question = tempStorage.classQuestions.find(q => q.id === questionId);
  
  if (question) {
    question.answered = true;
    question.answer = answer;
    question.answeredAt = Date.now();
    question.answeredBy = teacherId;
    return true;
  }
  
  return false;
}

/**
 * 点赞课堂问题
 * @param {string} questionId - 问题ID
 * @returns {Promise<boolean>} 是否点赞成功
 */
export async function likeClassQuestion(questionId) {
  const question = tempStorage.classQuestions.find(q => q.id === questionId);
  
  if (question) {
    question.likes++;
    return true;
  }
  
  return false;
}

/**
 * 发起抢答
 * @param {string} teacherId - 教师ID
 * @param {Object} quickResponse - 抢答信息 {title, description}
 * @returns {Promise<string>} 抢答ID
 */
export async function startQuickResponse(teacherId, quickResponse) {
  const responseObj = {
    id: Date.now() + Math.random().toString(36).substr(2, 9),
    teacherId,
    timestamp: Date.now(),
    status: 'active',
    responses: [],
    ...quickResponse
  };
  
  tempStorage.quickResponses.push(responseObj);
  return responseObj.id;
}

/**
 * 提交抢答
 * @param {string} studentId - 学生ID
 * @param {string} responseId - 抢答ID
 * @param {string} answer - 答案
 * @returns {Promise<boolean>} 是否提交成功
 */
export async function submitQuickResponse(studentId, responseId, answer) {
  const quickResponse = tempStorage.quickResponses.find(r => r.id === responseId);
  
  if (quickResponse && quickResponse.status === 'active') {
    // 检查是否已经提交过
    if (quickResponse.responses.some(r => r.studentId === studentId)) {
      return false;
    }
    
    quickResponse.responses.push({
      studentId,
      timestamp: Date.now(),
      answer
    });
    
    return true;
  }
  
  return false;
}

/**
 * 结束抢答
 * @param {string} teacherId - 教师ID
 * @param {string} responseId - 抢答ID
 * @returns {Promise<boolean>} 是否结束成功
 */
export async function endQuickResponse(teacherId, responseId) {
  const quickResponse = tempStorage.quickResponses.find(r => r.id === responseId);
  
  if (quickResponse && quickResponse.teacherId === teacherId) {
    quickResponse.status = 'ended';
    quickResponse.endTime = Date.now();
    return true;
  }
  
  return false;
}

/**
 * 获取抢答信息
 * @param {string} responseId - 抢答ID
 * @returns {Promise<Object>} 抢答信息
 */
export async function getQuickResponse(responseId) {
  return tempStorage.quickResponses.find(r => r.id === responseId) || null;
}

/**
 * 获取当前活跃的抢答
 * @returns {Promise<Object>} 抢答信息
 */
export async function getActiveQuickResponse() {
  return tempStorage.quickResponses.find(r => r.status === 'active') || null;
} 